# 修复图片加载问题指南

## 问题描述
Movie页面出现图片配置错误：`Invalid src prop (https://image.tmdb.org/t/p/w500/...) on 'next/image', hostname 'image.tmdb.org' is not configured under images in your 'next.config.js'`

## 解决方案

### 1. 已完成的修复
✅ 更新了 `next.config.ts` 文件，添加了 TMDB 图片域名配置：

```typescript
images: {
  domains: ['localhost', 'image.tmdb.org'],
  remotePatterns: [
    {
      protocol: 'https',
      hostname: 'image.tmdb.org',
      port: '',
      pathname: '/t/p/**',
    },
  ],
},
```

### 2. 需要重启开发服务器
配置更改后需要重启开发服务器才能生效：

```bash
# 停止当前服务器 (Ctrl+C)
# 然后重新启动
npm run dev
```

### 3. 导航图标优化
✅ 已完成图标样式优化：
- 图标尺寸：从14px缩小到12px
- 图标颜色：改为 `text-gray-500`，hover时为 `text-gray-300`
- 图标间距：增加到 `space-x-6`
- 与导航距离：增加到 `ml-16`

## 测试步骤

1. **重启开发服务器**：
   ```bash
   cd music-portfolio
   npm run dev
   ```

2. **测试图片加载**：
   - 访问 `/movie` 页面
   - 检查电影海报是否正常显示
   - 如果仍有问题，检查浏览器控制台错误

3. **测试导航图标**：
   - 检查右上角B站和LinkedIn图标大小
   - 确认图标颜色和间距符合要求
   - 测试hover效果

## 备用方案

如果外部图片仍然无法加载，可以：

1. **使用本地图片**：
   将图片下载到 `public/posters/` 目录
   
2. **修改海报API**：
   返回本地图片路径而不是外部URL

3. **添加更多域名**：
   如果使用其他图片源，在 `next.config.ts` 中添加相应域名

## 常见问题

**Q: 重启后仍然报错？**
A: 检查浏览器缓存，尝试硬刷新 (Ctrl+Shift+R)

**Q: 图标太小看不清？**
A: 可以调整 `width` 和 `height` 属性，建议保持在10-16px之间

**Q: 想要不同的图标颜色？**
A: 修改 `text-gray-500` 和 `hover:text-gray-300` 类名

## 文件修改记录

1. `next.config.ts` - 添加图片域名配置
2. `src/components/Navigation.tsx` - 优化图标样式
3. `public/posters/default.svg` - 默认海报图片（已存在）
