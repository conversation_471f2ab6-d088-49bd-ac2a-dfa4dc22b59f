'use client'

import { useEffect, useState } from 'react'

interface VideoDebugInfoProps {
  videoRef: React.RefObject<HTMLVideoElement>
  videoPath: string
  selectedSubtitle: string
}

export default function VideoDebugInfo({ videoRef, videoPath, selectedSubtitle }: VideoDebugInfoProps) {
  const [debugInfo, setDebugInfo] = useState({
    readyState: 0,
    networkState: 0,
    error: null as MediaError | null,
    currentSrc: '',
    textTracks: [] as any[],
    buffered: [] as any[],
  })

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const updateDebugInfo = () => {
      const bufferedRanges = []
      for (let i = 0; i < video.buffered.length; i++) {
        bufferedRanges.push({
          start: video.buffered.start(i),
          end: video.buffered.end(i)
        })
      }

      const textTracksInfo = []
      for (let i = 0; i < video.textTracks.length; i++) {
        const track = video.textTracks[i]
        textTracksInfo.push({
          kind: track.kind,
          label: track.label,
          language: track.language,
          mode: track.mode,
          cues: track.cues?.length || 0
        })
      }

      setDebugInfo({
        readyState: video.readyState,
        networkState: video.networkState,
        error: video.error,
        currentSrc: video.currentSrc,
        textTracks: textTracksInfo,
        buffered: bufferedRanges,
      })
    }

    // 监听各种事件
    const events = [
      'loadstart', 'loadedmetadata', 'loadeddata', 'canplay', 'canplaythrough',
      'error', 'stalled', 'waiting', 'progress', 'timeupdate'
    ]

    events.forEach(event => {
      video.addEventListener(event, updateDebugInfo)
    })

    // 初始更新
    updateDebugInfo()

    return () => {
      events.forEach(event => {
        video.removeEventListener(event, updateDebugInfo)
      })
    }
  }, [videoRef])

  const getReadyStateText = (state: number) => {
    switch (state) {
      case 0: return 'HAVE_NOTHING'
      case 1: return 'HAVE_METADATA'
      case 2: return 'HAVE_CURRENT_DATA'
      case 3: return 'HAVE_FUTURE_DATA'
      case 4: return 'HAVE_ENOUGH_DATA'
      default: return 'UNKNOWN'
    }
  }

  const getNetworkStateText = (state: number) => {
    switch (state) {
      case 0: return 'NETWORK_EMPTY'
      case 1: return 'NETWORK_IDLE'
      case 2: return 'NETWORK_LOADING'
      case 3: return 'NETWORK_NO_SOURCE'
      default: return 'UNKNOWN'
    }
  }

  return (
    <div className="fixed top-4 left-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-md z-50">
      <h3 className="font-bold mb-2">视频调试信息</h3>
      
      <div className="space-y-1">
        <div><strong>视频路径:</strong> {videoPath}</div>
        <div><strong>当前源:</strong> {debugInfo.currentSrc}</div>
        <div><strong>就绪状态:</strong> {getReadyStateText(debugInfo.readyState)} ({debugInfo.readyState})</div>
        <div><strong>网络状态:</strong> {getNetworkStateText(debugInfo.networkState)} ({debugInfo.networkState})</div>
        <div><strong>选中字幕:</strong> {selectedSubtitle}</div>
        
        {debugInfo.error && (
          <div className="text-red-400">
            <strong>错误:</strong> {debugInfo.error.message} (代码: {debugInfo.error.code})
          </div>
        )}
        
        <div><strong>缓冲区间:</strong></div>
        {debugInfo.buffered.map((range, i) => (
          <div key={i} className="ml-2">
            {range.start.toFixed(1)}s - {range.end.toFixed(1)}s
          </div>
        ))}
        
        <div><strong>字幕轨道:</strong></div>
        {debugInfo.textTracks.map((track, i) => (
          <div key={i} className="ml-2">
            {track.label} ({track.language}) - {track.mode} - {track.cues} cues
          </div>
        ))}
      </div>
    </div>
  )
}
