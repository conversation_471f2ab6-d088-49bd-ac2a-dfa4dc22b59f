{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/movies/poster/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const title = searchParams.get('title')\n\n    if (!title) {\n      return NextResponse.json(\n        { success: false, error: '电影名称不能为空' },\n        { status: 400 }\n      )\n    }\n\n    // 这里可以集成真实的电影数据库API，比如TMDB\n    // 目前返回一个模拟的海报URL\n    const posterUrl = await fetchMoviePoster(title)\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        title,\n        poster_url: posterUrl\n      }\n    })\n\n  } catch (error) {\n    console.error('获取电影海报失败:', error)\n    return NextResponse.json(\n      { success: false, error: '获取海报失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// 模拟获取电影海报的函数\n// 在实际应用中，这里可以调用TMDB API或其他电影数据库API\nasync function fetchMoviePoster(title: string): Promise<string> {\n  // 预定义一些常见电影的海报URL\n  const moviePosters: { [key: string]: string } = {\n    '教父': 'https://image.tmdb.org/t/p/w500/3bhkrj58Vtu7enYsRolD1fZdja1.jpg',\n    '肖申克的救赎': 'https://image.tmdb.org/t/p/w500/q6y0Go1tsGEsmtFryDOJo3dEmqu.jpg',\n    '泰坦尼克号': 'https://image.tmdb.org/t/p/w500/9xjZS2rlVxm8SFx8kPC3aIGCOYQ.jpg',\n    '辛德勒的名单': 'https://image.tmdb.org/t/p/w500/sF1U4EUQS8YHUYjNl3pMGNIQyr0.jpg',\n    '好家伙': 'https://image.tmdb.org/t/p/w500/aKuFiU82s5ISJpGZp7YkIr3kCUd.jpg',\n    '沉默的羔羊': 'https://image.tmdb.org/t/p/w500/uS9m8OBk1A8eM9I042bx8XXpqAq.jpg',\n    '阿甘正传': 'https://image.tmdb.org/t/p/w500/saHP97rTPS5eLmrLQEcANmKrsFl.jpg',\n    '勇敢的心': 'https://image.tmdb.org/t/p/w500/2qAgGeYdLjelOEqjW9FYvPHpplC.jpg',\n    '终结者2：审判日': 'https://image.tmdb.org/t/p/w500/5M0j0B18abtBI5gi2RhfjjurTqb.jpg',\n    '黄海': 'https://image.tmdb.org/t/p/w500/gQbpzKa8GvVfNuTKwgm5pBALgKu.jpg',\n    '新世界': 'https://image.tmdb.org/t/p/w500/8bXKGbPqYOdZWoKefe8LoGvAZUr.jpg',\n    '寄生虫': 'https://image.tmdb.org/t/p/w500/7IiTTgloJzvGI1TAYymCfbfl3vT.jpg',\n    '燃烧': 'https://image.tmdb.org/t/p/w500/4SYTH5FdB0dAORV98Nq4dAGOfiM.jpg',\n    '老男孩': 'https://image.tmdb.org/t/p/w500/PFyaPrIJvl2jjOON7xwHSKqtYU.jpg',\n    '我的野蛮女友': 'https://image.tmdb.org/t/p/w500/jjPJ4s3DWZZvI4vw8Xfi4Vqa1Q8.jpg',\n    '千与千寻': 'https://image.tmdb.org/t/p/w500/39wmItIWsg5sZMyRUHLkWBcuVCM.jpg',\n    '你的名字': 'https://image.tmdb.org/t/p/w500/q719jXXEzOoYaps6babgKnONONX.jpg',\n    '天气之子': 'https://image.tmdb.org/t/p/w500/1BdWKaKKkKOdAuLo1bpAl1hhwuG.jpg',\n    '攻壳机动队': 'https://image.tmdb.org/t/p/w500/myRzRzCxdfUWjkJWgpHHZ1oGkJd.jpg',\n    '海角七号': 'https://image.tmdb.org/t/p/w500/fJQqPZdYGomYO8WxJvVzHjpvdys.jpg',\n    '那些年，我们一起追的女孩': 'https://image.tmdb.org/t/p/w500/yImmxKJVzQjely1CvFGLmNpgJfF.jpg',\n    '艋舺': 'https://image.tmdb.org/t/p/w500/8bXKGbPqYOdZWoKefe8LoGvAZUr.jpg',\n    '赛德克·巴莱': 'https://image.tmdb.org/t/p/w500/gQbpzKa8GvVfNuTKwgm5pBALgKu.jpg'\n  }\n\n  // 检查是否有预定义的海报\n  if (moviePosters[title]) {\n    return moviePosters[title]\n  }\n\n  // 如果没有预定义的海报，返回默认海报\n  return '/posters/default.svg'\n}\n\n// 在实际应用中，可以使用TMDB API\n// async function fetchMoviePosterFromTMDB(title: string): Promise<string> {\n//   const API_KEY = process.env.TMDB_API_KEY\n//   if (!API_KEY) {\n//     throw new Error('TMDB API key not configured')\n//   }\n//\n//   try {\n//     const searchUrl = `https://api.themoviedb.org/3/search/movie?api_key=${API_KEY}&query=${encodeURIComponent(title)}&language=zh-CN`\n//     const response = await fetch(searchUrl)\n//     const data = await response.json()\n//\n//     if (data.results && data.results.length > 0) {\n//       const movie = data.results[0]\n//       if (movie.poster_path) {\n//         return `https://image.tmdb.org/t/p/w500${movie.poster_path}`\n//       }\n//     }\n//\n//     return '/posters/default.svg'\n//   } catch (error) {\n//     console.error('TMDB API error:', error)\n//     return '/posters/default.svg'\n//   }\n// }\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC;QAE/B,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,2BAA2B;QAC3B,iBAAiB;QACjB,MAAM,YAAY,MAAM,iBAAiB;QAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA,YAAY;YACd;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,cAAc;AACd,mCAAmC;AACnC,eAAe,iBAAiB,KAAa;IAC3C,kBAAkB;IAClB,MAAM,eAA0C;QAC9C,MAAM;QACN,UAAU;QACV,SAAS;QACT,UAAU;QACV,OAAO;QACP,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,MAAM;QACN,OAAO;QACP,OAAO;QACP,MAAM;QACN,OAAO;QACP,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,gBAAgB;QAChB,MAAM;QACN,UAAU;IACZ;IAEA,cAAc;IACd,IAAI,YAAY,CAAC,MAAM,EAAE;QACvB,OAAO,YAAY,CAAC,MAAM;IAC5B;IAEA,oBAAoB;IACpB,OAAO;AACT,EAEA,sBAAsB;CACtB,4EAA4E;CAC5E,6CAA6C;CAC7C,oBAAoB;CACpB,qDAAqD;CACrD,MAAM;CACN,EAAE;CACF,UAAU;CACV,yIAAyI;CACzI,8CAA8C;CAC9C,yCAAyC;CACzC,EAAE;CACF,qDAAqD;CACrD,sCAAsC;CACtC,iCAAiC;CACjC,uEAAuE;CACvE,UAAU;CACV,QAAQ;CACR,EAAE;CACF,oCAAoC;CACpC,sBAAsB;CACtB,8CAA8C;CAC9C,oCAAoC;CACpC,MAAM;CACN,IAAI", "debugId": null}}]}