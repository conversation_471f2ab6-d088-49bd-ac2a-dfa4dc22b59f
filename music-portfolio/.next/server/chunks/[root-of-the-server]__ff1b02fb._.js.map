{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/subtitles/generate/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { writeFile, mkdir } from 'fs/promises'\nimport { join } from 'path'\nimport { existsSync } from 'fs'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { videoPath, language } = await request.json()\n\n    if (!videoPath || !language) {\n      return NextResponse.json(\n        { success: false, error: '缺少必要参数' },\n        { status: 400 }\n      )\n    }\n\n    // 验证用户权限\n    const authHeader = request.headers.get('authorization')\n    if (!authHeader) {\n      return NextResponse.json(\n        { success: false, error: '未授权' },\n        { status: 401 }\n      )\n    }\n\n    // 生成字幕文件名\n    const fileName = videoPath.split('/').pop()?.replace(/\\.[^/.]+$/, '')\n    const subtitleFileName = `${fileName}_${language}.vtt`\n    \n    // 确保字幕目录存在\n    const subtitlesDir = join(process.cwd(), 'public', 'subtitles')\n    if (!existsSync(subtitlesDir)) {\n      await mkdir(subtitlesDir, { recursive: true })\n    }\n\n    const subtitlePath = join(subtitlesDir, subtitleFileName)\n\n    // 生成示例字幕内容（实际应用中这里会调用AI服务）\n    const subtitleContent = generateSampleSubtitles(language)\n\n    // 写入字幕文件\n    await writeFile(subtitlePath, subtitleContent, 'utf-8')\n\n    return NextResponse.json({\n      success: true,\n      subtitlePath: `/subtitles/${subtitleFileName}`\n    })\n\n  } catch (error) {\n    console.error('字幕生成失败:', error)\n    return NextResponse.json(\n      { success: false, error: '字幕生成失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// 生成示例字幕内容\nfunction generateSampleSubtitles(language: string): string {\n  let subtitles: Array<{start: string, end: string, text: string}> = []\n\n  switch (language) {\n    case 'zh':\n      subtitles = [\n        { start: '00:00:00.000', end: '00:00:03.000', text: '您好，感谢您观看这部电影。' },\n        { start: '00:00:03.000', end: '00:00:06.000', text: '这些字幕是自动生成的。' },\n        { start: '00:00:06.000', end: '00:00:09.000', text: '字幕将根据实际电影内容进行调整。' },\n        { start: '00:00:09.000', end: '00:00:12.000', text: '祝您观影愉快！' }\n      ]\n      break\n    case 'ko':\n      subtitles = [\n        { start: '00:00:00.000', end: '00:00:03.000', text: '안녕하세요, 이 영화를 시청해 주셔서 감사합니다.' },\n        { start: '00:00:03.000', end: '00:00:06.000', text: '이 자막은 자동으로 생성되었습니다.' },\n        { start: '00:00:06.000', end: '00:00:09.000', text: '실제 영화 내용에 따라 자막이 조정됩니다.' },\n        { start: '00:00:09.000', end: '00:00:12.000', text: '즐거운 시청 되세요!' }\n      ]\n      break\n    case 'en':\n      subtitles = [\n        { start: '00:00:00.000', end: '00:00:03.000', text: 'Hello, thank you for watching this movie.' },\n        { start: '00:00:03.000', end: '00:00:06.000', text: 'These subtitles were automatically generated.' },\n        { start: '00:00:06.000', end: '00:00:09.000', text: 'Subtitles will be adjusted according to the actual movie content.' },\n        { start: '00:00:09.000', end: '00:00:12.000', text: 'Enjoy watching!' }\n      ]\n      break\n    case 'zh-ko':\n      subtitles = [\n        { start: '00:00:00.000', end: '00:00:03.000', text: '您好，感谢您观看这部电影。\\n안녕하세요, 이 영화를 시청해 주셔서 감사합니다.' },\n        { start: '00:00:03.000', end: '00:00:06.000', text: '这些字幕是自动生成的。\\n이 자막은 자동으로 생성되었습니다.' },\n        { start: '00:00:06.000', end: '00:00:09.000', text: '字幕将根据实际电影内容进行调整。\\n실제 영화 내용에 따라 자막이 조정됩니다.' },\n        { start: '00:00:09.000', end: '00:00:12.000', text: '祝您观影愉快！\\n즐거운 시청 되세요!' }\n      ]\n      break\n    case 'zh-en':\n      subtitles = [\n        { start: '00:00:00.000', end: '00:00:03.000', text: '您好，感谢您观看这部电影。\\nHello, thank you for watching this movie.' },\n        { start: '00:00:03.000', end: '00:00:06.000', text: '这些字幕是自动生成的。\\nThese subtitles were automatically generated.' },\n        { start: '00:00:06.000', end: '00:00:09.000', text: '字幕将根据实际电影内容进行调整。\\nSubtitles will be adjusted according to the actual movie content.' },\n        { start: '00:00:09.000', end: '00:00:12.000', text: '祝您观影愉快！\\nEnjoy watching!' }\n      ]\n      break\n    default:\n      subtitles = [\n        { start: '00:00:00.000', end: '00:00:03.000', text: 'Hello, thank you for watching this movie.' },\n        { start: '00:00:03.000', end: '00:00:06.000', text: 'These subtitles were automatically generated.' },\n        { start: '00:00:06.000', end: '00:00:09.000', text: 'Subtitles will be adjusted according to the actual movie content.' },\n        { start: '00:00:09.000', end: '00:00:12.000', text: 'Enjoy watching!' }\n      ]\n  }\n\n  let vttContent = 'WEBVTT\\n\\n'\n\n  subtitles.forEach((subtitle, index) => {\n    vttContent += `${index + 1}\\n`\n    vttContent += `${subtitle.start} --> ${subtitle.end}\\n`\n    vttContent += `${subtitle.text}\\n\\n`\n  })\n\n  return vttContent\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElD,IAAI,CAAC,aAAa,CAAC,UAAU;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAS,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAM,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,UAAU;QACV,MAAM,WAAW,UAAU,KAAK,CAAC,KAAK,GAAG,IAAI,QAAQ,aAAa;QAClE,MAAM,mBAAmB,GAAG,SAAS,CAAC,EAAE,SAAS,IAAI,CAAC;QAEtD,WAAW;QACX,MAAM,eAAe,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI,UAAU;QACnD,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,eAAe;YAC7B,MAAM,CAAA,GAAA,qHAAA,CAAA,QAAK,AAAD,EAAE,cAAc;gBAAE,WAAW;YAAK;QAC9C;QAEA,MAAM,eAAe,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,cAAc;QAExC,2BAA2B;QAC3B,MAAM,kBAAkB,wBAAwB;QAEhD,SAAS;QACT,MAAM,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,cAAc,iBAAiB;QAE/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,cAAc,CAAC,WAAW,EAAE,kBAAkB;QAChD;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAS,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,WAAW;AACX,SAAS,wBAAwB,QAAgB;IAC/C,IAAI,YAA+D,EAAE;IAErE,OAAQ;QACN,KAAK;YACH,YAAY;gBACV;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAgB;gBACpE;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAc;gBAClE;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAmB;gBACvE;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAU;aAC/D;YACD;QACF,KAAK;YACH,YAAY;gBACV;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAA8B;gBAClF;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAsB;gBAC1E;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAA0B;gBAC9E;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAc;aACnE;YACD;QACF,KAAK;YACH,YAAY;gBACV;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAA4C;gBAChG;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAgD;gBACpG;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAoE;gBACxH;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAkB;aACvE;YACD;QACF,KAAK;YACH,YAAY;gBACV;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAA6C;gBACjG;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAmC;gBACvF;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAA4C;gBAChG;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAuB;aAC5E;YACD;QACF,KAAK;YACH,YAAY;gBACV;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAA2D;gBAC/G;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAA6D;gBACjH;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAsF;gBAC1I;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAA2B;aAChF;YACD;QACF;YACE,YAAY;gBACV;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAA4C;gBAChG;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAgD;gBACpG;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAoE;gBACxH;oBAAE,OAAO;oBAAgB,KAAK;oBAAgB,MAAM;gBAAkB;aACvE;IACL;IAEA,IAAI,aAAa;IAEjB,UAAU,OAAO,CAAC,CAAC,UAAU;QAC3B,cAAc,GAAG,QAAQ,EAAE,EAAE,CAAC;QAC9B,cAAc,GAAG,SAAS,KAAK,CAAC,KAAK,EAAE,SAAS,GAAG,CAAC,EAAE,CAAC;QACvD,cAAc,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC;IACtC;IAEA,OAAO;AACT", "debugId": null}}]}