module.exports = {

"[project]/.next-internal/server/app/api/subtitles/generate/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs/promises [external] (fs/promises, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs/promises", () => require("fs/promises"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[project]/src/app/api/subtitles/generate/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs/promises [external] (fs/promises, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
;
;
;
;
async function POST(request) {
    try {
        const { videoPath, language } = await request.json();
        if (!videoPath || !language) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '缺少必要参数'
            }, {
                status: 400
            });
        }
        // 验证用户权限
        const authHeader = request.headers.get('authorization');
        if (!authHeader) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '未授权'
            }, {
                status: 401
            });
        }
        // 生成字幕文件名
        const fileName = videoPath.split('/').pop()?.replace(/\.[^/.]+$/, '');
        const subtitleFileName = `${fileName}_${language}.vtt`;
        // 确保字幕目录存在
        const subtitlesDir = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(process.cwd(), 'public', 'subtitles');
        if (!(0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["existsSync"])(subtitlesDir)) {
            await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["mkdir"])(subtitlesDir, {
                recursive: true
            });
        }
        const subtitlePath = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(subtitlesDir, subtitleFileName);
        // 生成示例字幕内容（实际应用中这里会调用AI服务）
        const subtitleContent = generateSampleSubtitles(language);
        // 写入字幕文件
        await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["writeFile"])(subtitlePath, subtitleContent, 'utf-8');
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            subtitlePath: `/subtitles/${subtitleFileName}`
        });
    } catch (error) {
        console.error('字幕生成失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '字幕生成失败'
        }, {
            status: 500
        });
    }
}
// 生成示例字幕内容
function generateSampleSubtitles(language) {
    let subtitles = [];
    switch(language){
        case 'zh':
            subtitles = [
                {
                    start: '00:00:00.000',
                    end: '00:00:03.000',
                    text: '您好，感谢您观看这部电影。'
                },
                {
                    start: '00:00:03.000',
                    end: '00:00:06.000',
                    text: '这些字幕是自动生成的。'
                },
                {
                    start: '00:00:06.000',
                    end: '00:00:09.000',
                    text: '字幕将根据实际电影内容进行调整。'
                },
                {
                    start: '00:00:09.000',
                    end: '00:00:12.000',
                    text: '祝您观影愉快！'
                }
            ];
            break;
        case 'ko':
            subtitles = [
                {
                    start: '00:00:00.000',
                    end: '00:00:03.000',
                    text: '안녕하세요, 이 영화를 시청해 주셔서 감사합니다.'
                },
                {
                    start: '00:00:03.000',
                    end: '00:00:06.000',
                    text: '이 자막은 자동으로 생성되었습니다.'
                },
                {
                    start: '00:00:06.000',
                    end: '00:00:09.000',
                    text: '실제 영화 내용에 따라 자막이 조정됩니다.'
                },
                {
                    start: '00:00:09.000',
                    end: '00:00:12.000',
                    text: '즐거운 시청 되세요!'
                }
            ];
            break;
        case 'en':
            subtitles = [
                {
                    start: '00:00:00.000',
                    end: '00:00:03.000',
                    text: 'Hello, thank you for watching this movie.'
                },
                {
                    start: '00:00:03.000',
                    end: '00:00:06.000',
                    text: 'These subtitles were automatically generated.'
                },
                {
                    start: '00:00:06.000',
                    end: '00:00:09.000',
                    text: 'Subtitles will be adjusted according to the actual movie content.'
                },
                {
                    start: '00:00:09.000',
                    end: '00:00:12.000',
                    text: 'Enjoy watching!'
                }
            ];
            break;
        case 'zh-ko':
            subtitles = [
                {
                    start: '00:00:00.000',
                    end: '00:00:03.000',
                    text: '您好，感谢您观看这部电影。\n안녕하세요, 이 영화를 시청해 주셔서 감사합니다.'
                },
                {
                    start: '00:00:03.000',
                    end: '00:00:06.000',
                    text: '这些字幕是自动生成的。\n이 자막은 자동으로 생성되었습니다.'
                },
                {
                    start: '00:00:06.000',
                    end: '00:00:09.000',
                    text: '字幕将根据实际电影内容进行调整。\n실제 영화 내용에 따라 자막이 조정됩니다.'
                },
                {
                    start: '00:00:09.000',
                    end: '00:00:12.000',
                    text: '祝您观影愉快！\n즐거운 시청 되세요!'
                }
            ];
            break;
        case 'zh-en':
            subtitles = [
                {
                    start: '00:00:00.000',
                    end: '00:00:03.000',
                    text: '您好，感谢您观看这部电影。\nHello, thank you for watching this movie.'
                },
                {
                    start: '00:00:03.000',
                    end: '00:00:06.000',
                    text: '这些字幕是自动生成的。\nThese subtitles were automatically generated.'
                },
                {
                    start: '00:00:06.000',
                    end: '00:00:09.000',
                    text: '字幕将根据实际电影内容进行调整。\nSubtitles will be adjusted according to the actual movie content.'
                },
                {
                    start: '00:00:09.000',
                    end: '00:00:12.000',
                    text: '祝您观影愉快！\nEnjoy watching!'
                }
            ];
            break;
        default:
            subtitles = [
                {
                    start: '00:00:00.000',
                    end: '00:00:03.000',
                    text: 'Hello, thank you for watching this movie.'
                },
                {
                    start: '00:00:03.000',
                    end: '00:00:06.000',
                    text: 'These subtitles were automatically generated.'
                },
                {
                    start: '00:00:06.000',
                    end: '00:00:09.000',
                    text: 'Subtitles will be adjusted according to the actual movie content.'
                },
                {
                    start: '00:00:09.000',
                    end: '00:00:12.000',
                    text: 'Enjoy watching!'
                }
            ];
    }
    let vttContent = 'WEBVTT\n\n';
    subtitles.forEach((subtitle, index)=>{
        vttContent += `${index + 1}\n`;
        vttContent += `${subtitle.start} --> ${subtitle.end}\n`;
        vttContent += `${subtitle.text}\n\n`;
    });
    return vttContent;
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__ff1b02fb._.js.map