{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/lib/auth.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\n\nexport interface AdminSession {\n  isAuthenticated: boolean\n  username?: string\n}\n\nexport function checkAdminAuth(request: NextRequest): AdminSession {\n  const authHeader = request.headers.get('authorization')\n  \n  if (!authHeader || !authHeader.startsWith('Basic ')) {\n    return { isAuthenticated: false }\n  }\n\n  try {\n    const base64Credentials = authHeader.slice(6)\n    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii')\n    const [username, password] = credentials.split(':')\n\n    const adminUsername = process.env.ADMIN_USERNAME || 'admin'\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123'\n\n    if (username === adminUsername && password === adminPassword) {\n      return { isAuthenticated: true, username }\n    }\n\n    return { isAuthenticated: false }\n  } catch (error) {\n    return { isAuthenticated: false }\n  }\n}\n\nexport function requireAuth() {\n  return new Response('Unauthorized', {\n    status: 401,\n    headers: {\n      'WWW-Authenticate': 'Basic realm=\"Admin Area\"',\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;AAOO,SAAS,eAAe,OAAoB;IACjD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,WAAW;QACnD,OAAO;YAAE,iBAAiB;QAAM;IAClC;IAEA,IAAI;QACF,MAAM,oBAAoB,WAAW,KAAK,CAAC;QAC3C,MAAM,cAAc,OAAO,IAAI,CAAC,mBAAmB,UAAU,QAAQ,CAAC;QACtE,MAAM,CAAC,UAAU,SAAS,GAAG,YAAY,KAAK,CAAC;QAE/C,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QACpD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QAEpD,IAAI,aAAa,iBAAiB,aAAa,eAAe;YAC5D,OAAO;gBAAE,iBAAiB;gBAAM;YAAS;QAC3C;QAEA,OAAO;YAAE,iBAAiB;QAAM;IAClC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,iBAAiB;QAAM;IAClC;AACF;AAEO,SAAS;IACd,OAAO,IAAI,SAAS,gBAAgB;QAClC,QAAQ;QACR,SAAS;YACP,oBAAoB;QACtB;IACF;AACF", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/video/%5B...path%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { readFile, stat } from 'fs/promises'\nimport { existsSync } from 'fs'\nimport path from 'path'\nimport { checkAdminAuth, requireAuth } from '@/lib/auth'\n\n// 处理CORS预检请求\nexport async function OPTIONS(request: NextRequest) {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',\n      'Access-Control-Allow-Headers': 'Range, Authorization, Content-Type',\n      'Access-Control-Max-Age': '86400',\n    },\n  })\n}\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ path: string[] }> }\n) {\n  // 等待 params 解析\n  const resolvedParams = await params\n  try {\n    // 检查管理员权限 - 支持多种认证方式\n    let auth = checkAdminAuth(request)\n\n    // 如果header认证失败，尝试从查询参数获取认证信息\n    if (!auth.isAuthenticated) {\n      const { searchParams } = new URL(request.url)\n      const authParam = searchParams.get('auth')\n      if (authParam) {\n        try {\n          const credentials = atob(decodeURIComponent(authParam))\n          const [username, password] = credentials.split(':')\n          if (username === 'admin' && password === 'admin123') {\n            auth = { isAuthenticated: true, username }\n          }\n        } catch (e) {\n          // 忽略解码错误\n        }\n      }\n    }\n\n    if (!auth.isAuthenticated) {\n      return new NextResponse('Unauthorized', { status: 401 })\n    }\n\n    // 构建文件路径\n    const filePath = resolvedParams.path.join('/')\n    const fullPath = path.join(process.cwd(), 'public', 'uploads', 'movies', filePath)\n\n    // 检查文件是否存在\n    if (!existsSync(fullPath)) {\n      return NextResponse.json(\n        { error: 'Video file not found' },\n        { status: 404 }\n      )\n    }\n\n    // 获取文件信息\n    const fileStats = await stat(fullPath)\n    const fileSize = fileStats.size\n\n    // 处理Range请求（用于视频流）\n    const range = request.headers.get('range')\n\n    if (range) {\n      // 解析Range头\n      const parts = range.replace(/bytes=/, '').split('-')\n      const start = parseInt(parts[0], 10)\n      // 使用更小的块大小减少内存占用\n      const end = parts[1] ? parseInt(parts[1], 10) : Math.min(start + 512 * 1024, fileSize - 1) // 512KB chunks\n      const chunkSize = (end - start) + 1\n\n      // 使用流式读取，避免一次性加载整个文件\n      const fs = require('fs')\n      const stream = fs.createReadStream(fullPath, { start, end })\n\n      // 返回流式响应\n      return new NextResponse(stream as any, {\n        status: 206,\n        headers: {\n          'Content-Range': `bytes ${start}-${end}/${fileSize}`,\n          'Accept-Ranges': 'bytes',\n          'Content-Length': chunkSize.toString(),\n          'Content-Type': getContentType(fullPath),\n          'Cache-Control': 'public, max-age=31536000', // 1年缓存\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',\n          'Access-Control-Allow-Headers': 'Range',\n        },\n      })\n    } else {\n      // 对于非Range请求，只返回头部信息，强制客户端使用Range请求\n      return new NextResponse(null, {\n        status: 200,\n        headers: {\n          'Content-Length': fileSize.toString(),\n          'Content-Type': getContentType(fullPath),\n          'Accept-Ranges': 'bytes',\n          'Cache-Control': 'public, max-age=31536000',\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',\n          'Access-Control-Allow-Headers': 'Range',\n        },\n      })\n    }\n\n  } catch (error) {\n    console.error('Error serving video:', error)\n    return NextResponse.json(\n      { error: 'Failed to serve video file' },\n      { status: 500 }\n    )\n  }\n}\n\n// 根据文件扩展名获取Content-Type\nfunction getContentType(filePath: string): string {\n  const ext = path.extname(filePath).toLowerCase()\n  \n  switch (ext) {\n    case '.mp4':\n      return 'video/mp4'\n    case '.mov':\n      return 'video/quicktime'\n    case '.avi':\n      return 'video/x-msvideo'\n    case '.webm':\n      return 'video/webm'\n    case '.wmv':\n      return 'video/x-ms-wmv'\n    case '.flv':\n      return 'video/x-flv'\n    default:\n      return 'video/mp4'\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,QAAQ,OAAoB;IAChD,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;YAChC,0BAA0B;QAC5B;IACF;AACF;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA2C;IAEnD,eAAe;IACf,MAAM,iBAAiB,MAAM;IAC7B,IAAI;QACF,qBAAqB;QACrB,IAAI,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;QAE1B,6BAA6B;QAC7B,IAAI,CAAC,KAAK,eAAe,EAAE;YACzB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;YAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;YACnC,IAAI,WAAW;gBACb,IAAI;oBACF,MAAM,cAAc,KAAK,mBAAmB;oBAC5C,MAAM,CAAC,UAAU,SAAS,GAAG,YAAY,KAAK,CAAC;oBAC/C,IAAI,aAAa,WAAW,aAAa,YAAY;wBACnD,OAAO;4BAAE,iBAAiB;4BAAM;wBAAS;oBAC3C;gBACF,EAAE,OAAO,GAAG;gBACV,SAAS;gBACX;YACF;QACF;QAEA,IAAI,CAAC,KAAK,eAAe,EAAE;YACzB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,gBAAgB;gBAAE,QAAQ;YAAI;QACxD;QAEA,SAAS;QACT,MAAM,WAAW,eAAe,IAAI,CAAC,IAAI,CAAC;QAC1C,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU,WAAW,UAAU;QAEzE,WAAW;QACX,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,WAAW;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,YAAY,MAAM,CAAA,GAAA,qHAAA,CAAA,OAAI,AAAD,EAAE;QAC7B,MAAM,WAAW,UAAU,IAAI;QAE/B,mBAAmB;QACnB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC;QAElC,IAAI,OAAO;YACT,WAAW;YACX,MAAM,QAAQ,MAAM,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC;YAChD,MAAM,QAAQ,SAAS,KAAK,CAAC,EAAE,EAAE;YACjC,iBAAiB;YACjB,MAAM,MAAM,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,EAAE,MAAM,KAAK,GAAG,CAAC,QAAQ,MAAM,MAAM,WAAW,GAAG,eAAe;;YAC1G,MAAM,YAAY,AAAC,MAAM,QAAS;YAElC,qBAAqB;YACrB,MAAM;YACN,MAAM,SAAS,GAAG,gBAAgB,CAAC,UAAU;gBAAE;gBAAO;YAAI;YAE1D,SAAS;YACT,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,QAAe;gBACrC,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE,UAAU;oBACpD,iBAAiB;oBACjB,kBAAkB,UAAU,QAAQ;oBACpC,gBAAgB,eAAe;oBAC/B,iBAAiB;oBACjB,+BAA+B;oBAC/B,gCAAgC;oBAChC,gCAAgC;gBAClC;YACF;QACF,OAAO;YACL,oCAAoC;YACpC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;gBAC5B,QAAQ;gBACR,SAAS;oBACP,kBAAkB,SAAS,QAAQ;oBACnC,gBAAgB,eAAe;oBAC/B,iBAAiB;oBACjB,iBAAiB;oBACjB,+BAA+B;oBAC/B,gCAAgC;oBAChC,gCAAgC;gBAClC;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA6B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,wBAAwB;AACxB,SAAS,eAAe,QAAgB;IACtC,MAAM,MAAM,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,UAAU,WAAW;IAE9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}]}