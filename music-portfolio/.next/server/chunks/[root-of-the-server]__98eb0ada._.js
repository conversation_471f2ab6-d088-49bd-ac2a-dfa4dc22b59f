module.exports = {

"[project]/.next-internal/server/app/api/movies/poster/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/movies/poster/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const title = searchParams.get('title');
        if (!title) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '电影名称不能为空'
            }, {
                status: 400
            });
        }
        // 这里可以集成真实的电影数据库API，比如TMDB
        // 目前返回一个模拟的海报URL
        const posterUrl = await fetchMoviePoster(title);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                title,
                poster_url: posterUrl
            }
        });
    } catch (error) {
        console.error('获取电影海报失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '获取海报失败'
        }, {
            status: 500
        });
    }
}
// 模拟获取电影海报的函数
// 在实际应用中，这里可以调用TMDB API或其他电影数据库API
async function fetchMoviePoster(title) {
    // 预定义一些常见电影的海报URL
    const moviePosters = {
        '教父': 'https://image.tmdb.org/t/p/w500/3bhkrj58Vtu7enYsRolD1fZdja1.jpg',
        '肖申克的救赎': 'https://image.tmdb.org/t/p/w500/q6y0Go1tsGEsmtFryDOJo3dEmqu.jpg',
        '泰坦尼克号': 'https://image.tmdb.org/t/p/w500/9xjZS2rlVxm8SFx8kPC3aIGCOYQ.jpg',
        '辛德勒的名单': 'https://image.tmdb.org/t/p/w500/sF1U4EUQS8YHUYjNl3pMGNIQyr0.jpg',
        '好家伙': 'https://image.tmdb.org/t/p/w500/aKuFiU82s5ISJpGZp7YkIr3kCUd.jpg',
        '沉默的羔羊': 'https://image.tmdb.org/t/p/w500/uS9m8OBk1A8eM9I042bx8XXpqAq.jpg',
        '阿甘正传': 'https://image.tmdb.org/t/p/w500/saHP97rTPS5eLmrLQEcANmKrsFl.jpg',
        '勇敢的心': 'https://image.tmdb.org/t/p/w500/2qAgGeYdLjelOEqjW9FYvPHpplC.jpg',
        '终结者2：审判日': 'https://image.tmdb.org/t/p/w500/5M0j0B18abtBI5gi2RhfjjurTqb.jpg',
        '黄海': 'https://image.tmdb.org/t/p/w500/gQbpzKa8GvVfNuTKwgm5pBALgKu.jpg',
        '新世界': 'https://image.tmdb.org/t/p/w500/8bXKGbPqYOdZWoKefe8LoGvAZUr.jpg',
        '寄生虫': 'https://image.tmdb.org/t/p/w500/7IiTTgloJzvGI1TAYymCfbfl3vT.jpg',
        '燃烧': 'https://image.tmdb.org/t/p/w500/4SYTH5FdB0dAORV98Nq4dAGOfiM.jpg',
        '老男孩': 'https://image.tmdb.org/t/p/w500/PFyaPrIJvl2jjOON7xwHSKqtYU.jpg',
        '我的野蛮女友': 'https://image.tmdb.org/t/p/w500/jjPJ4s3DWZZvI4vw8Xfi4Vqa1Q8.jpg',
        '千与千寻': 'https://image.tmdb.org/t/p/w500/39wmItIWsg5sZMyRUHLkWBcuVCM.jpg',
        '你的名字': 'https://image.tmdb.org/t/p/w500/q719jXXEzOoYaps6babgKnONONX.jpg',
        '天气之子': 'https://image.tmdb.org/t/p/w500/1BdWKaKKkKOdAuLo1bpAl1hhwuG.jpg',
        '攻壳机动队': 'https://image.tmdb.org/t/p/w500/myRzRzCxdfUWjkJWgpHHZ1oGkJd.jpg',
        '海角七号': 'https://image.tmdb.org/t/p/w500/fJQqPZdYGomYO8WxJvVzHjpvdys.jpg',
        '那些年，我们一起追的女孩': 'https://image.tmdb.org/t/p/w500/yImmxKJVzQjely1CvFGLmNpgJfF.jpg',
        '艋舺': 'https://image.tmdb.org/t/p/w500/8bXKGbPqYOdZWoKefe8LoGvAZUr.jpg',
        '赛德克·巴莱': 'https://image.tmdb.org/t/p/w500/gQbpzKa8GvVfNuTKwgm5pBALgKu.jpg'
    };
    // 检查是否有预定义的海报
    if (moviePosters[title]) {
        return moviePosters[title];
    }
    // 如果没有预定义的海报，返回默认海报
    return '/posters/default.svg';
} // 在实际应用中，可以使用TMDB API
 // async function fetchMoviePosterFromTMDB(title: string): Promise<string> {
 //   const API_KEY = process.env.TMDB_API_KEY
 //   if (!API_KEY) {
 //     throw new Error('TMDB API key not configured')
 //   }
 //
 //   try {
 //     const searchUrl = `https://api.themoviedb.org/3/search/movie?api_key=${API_KEY}&query=${encodeURIComponent(title)}&language=zh-CN`
 //     const response = await fetch(searchUrl)
 //     const data = await response.json()
 //
 //     if (data.results && data.results.length > 0) {
 //       const movie = data.results[0]
 //       if (movie.poster_path) {
 //         return `https://image.tmdb.org/t/p/w500${movie.poster_path}`
 //       }
 //     }
 //
 //     return '/posters/default.svg'
 //   } catch (error) {
 //     console.error('TMDB API error:', error)
 //     return '/posters/default.svg'
 //   }
 // }
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__98eb0ada._.js.map