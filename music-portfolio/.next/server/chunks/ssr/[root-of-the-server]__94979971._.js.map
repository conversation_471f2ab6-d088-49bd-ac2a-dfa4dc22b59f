{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Link from \"next/link\";\nimport { Play, Music, Film } from \"lucide-react\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"relative h-screen flex items-center justify-center bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900\">\n        {/* Background Image */}\n        <div className=\"absolute inset-0\">\n          <Image\n            src=\"/electric-guitar-bg.jpg\"\n            alt=\"Electric Guitar Background\"\n            fill\n            className=\"object-cover object-center\"\n            priority\n          />\n          <div className=\"absolute inset-0 bg-black/70\"></div>\n        </div>\n\n        <div className=\"relative z-10 text-center max-w-4xl mx-auto px-4\">\n          <h1 className=\"text-6xl md:text-8xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent font-serif italic tracking-wider\">\n            KIMAHALA\n          </h1>\n          <p className=\"text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed tracking-wide\">\n            MUSICIAN · CONTENT CREATOR\n          </p>\n          <p className=\"text-lg text-gray-400 mb-12 max-w-2xl mx-auto\">\n            Expressing emotions through music, sharing cinematic experiences.\n            Welcome to my world of original music and movie recommendations.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/music\"\n              className=\"inline-flex items-center px-8 py-4 bg-red-600 text-white font-semibold tracking-wider hover:bg-red-700 transition-colors duration-200\"\n            >\n              <Music className=\"mr-2\" size={20} />\n              EXPLORE MUSIC\n            </Link>\n            <Link\n              href=\"/movie\"\n              className=\"inline-flex items-center px-8 py-4 border-2 border-red-600 text-red-600 font-semibold tracking-wider hover:bg-red-600 hover:text-white transition-colors duration-200\"\n            >\n              <Film className=\"mr-2\" size={20} />\n              EXPLORE MOVIES\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Content */}\n      <section className=\"py-20 bg-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <h2 className=\"text-4xl font-bold text-center mb-16 tracking-wider\">FEATURED WORKS</h2>\n\n          <div className=\"grid md:grid-cols-2 gap-12\">\n            {/* Latest Music */}\n            <div className=\"bg-gray-900 rounded-lg p-8 hover:bg-gray-700 transition-colors duration-200\">\n              <div className=\"flex items-center mb-6\">\n                <Music className=\"mr-3 text-red-400\" size={24} />\n                <h3 className=\"text-2xl font-semibold tracking-wide\">LATEST MUSIC</h3>\n              </div>\n              <p className=\"text-gray-400 mb-6\">\n                Listen to my latest musical creations and feel the power of emotions expressed through sound.\n              </p>\n              <Link\n                href=\"/music\"\n                className=\"inline-flex items-center text-red-400 hover:text-red-300 font-medium tracking-wide\"\n              >\n                <Play className=\"mr-2\" size={16} />\n                PLAY NOW\n              </Link>\n            </div>\n\n            {/* Latest Movies */}\n            <div className=\"bg-gray-900 rounded-lg p-8 hover:bg-gray-700 transition-colors duration-200\">\n              <div className=\"flex items-center mb-6\">\n                <Film className=\"mr-3 text-red-400\" size={24} />\n                <h3 className=\"text-2xl font-semibold tracking-wide\">MOVIE RECOMMENDATIONS</h3>\n              </div>\n              <p className=\"text-gray-400 mb-6\">\n                Discover my curated collection of films that inspire and move me, from classic masterpieces to modern gems.\n              </p>\n              <Link\n                href=\"/movie\"\n                className=\"inline-flex items-center text-red-400 hover:text-red-300 font-medium tracking-wide\"\n              >\n                <Play className=\"mr-2\" size={16} />\n                EXPLORE NOW\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,IAAI;gCACJ,WAAU;gCACV,QAAQ;;;;;;0CAEV,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6I;;;;;;0CAG3J,8OAAC;gCAAE,WAAU;0CAAuE;;;;;;0CAGpF,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAI7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CAAM;;;;;;;kDAGtC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;gDAAO,MAAM;;;;;;4CAAM;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAEpE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;oDAAoB,MAAM;;;;;;8DAC3C,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;;;;;;;8CAMvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;oDAAoB,MAAM;;;;;;8DAC1C,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;;;;;;;sDAEvD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}]}