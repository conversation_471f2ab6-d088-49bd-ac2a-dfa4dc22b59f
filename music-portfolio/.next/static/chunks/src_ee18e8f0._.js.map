{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/components/VideoPlayer.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { Play, Pause, Volume2, VolumeX, Maximize, X, Subtitles } from 'lucide-react'\nimport VideoDebugInfo from './VideoDebugInfo'\n\ninterface VideoPlayerProps {\n  videoPath: string\n  title: string\n  onClose: () => void\n}\n\ninterface SubtitleTrack {\n  id: string\n  label: string\n  language: string\n  src: string\n}\n\nexport default function VideoPlayer({ videoPath, title, onClose }: VideoPlayerProps) {\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const [isPlaying, setIsPlaying] = useState(false)\n  const [currentTime, setCurrentTime] = useState(0)\n  const [duration, setDuration] = useState(0)\n  const [volume, setVolume] = useState(1)\n  const [isMuted, setIsMuted] = useState(false)\n  const [showControls, setShowControls] = useState(true)\n  const [isFullscreen, setIsFullscreen] = useState(false)\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const [showSubtitleMenu, setShowSubtitleMenu] = useState(false)\n  const [selectedSubtitle, setSelectedSubtitle] = useState<string>('off')\n  const [subtitleTracks, setSubtitleTracks] = useState<SubtitleTrack[]>([])\n  const [isGeneratingSubtitles, setIsGeneratingSubtitles] = useState(false)\n\n  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null)\n\n  useEffect(() => {\n    const video = videoRef.current\n    if (!video) return\n\n    const handleLoadedMetadata = () => {\n      setDuration(video.duration)\n      setIsLoading(false)\n      // 初始化字幕轨道\n      initializeSubtitleTracks()\n    }\n\n    const handleTimeUpdate = () => {\n      setCurrentTime(video.currentTime)\n    }\n\n    const handleError = () => {\n      setError('视频加载失败，请检查文件是否存在')\n      setIsLoading(false)\n    }\n\n    const handleLoadStart = () => {\n      setIsLoading(true)\n      setError(null)\n    }\n\n    // 监听全屏状态变化\n    const handleFullscreenChange = () => {\n      setIsFullscreen(!!document.fullscreenElement)\n    }\n\n    // 监听键盘事件\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (e.key === 'Escape' && document.fullscreenElement) {\n        document.exitFullscreen()\n      } else if (e.key === ' ') {\n        e.preventDefault()\n        togglePlay()\n      }\n    }\n\n    video.addEventListener('loadedmetadata', handleLoadedMetadata)\n    video.addEventListener('timeupdate', handleTimeUpdate)\n    video.addEventListener('error', handleError)\n    video.addEventListener('loadstart', handleLoadStart)\n    document.addEventListener('fullscreenchange', handleFullscreenChange)\n    document.addEventListener('keydown', handleKeyDown)\n\n    return () => {\n      video.removeEventListener('loadedmetadata', handleLoadedMetadata)\n      video.removeEventListener('timeupdate', handleTimeUpdate)\n      video.removeEventListener('error', handleError)\n      video.removeEventListener('loadstart', handleLoadStart)\n      document.removeEventListener('fullscreenchange', handleFullscreenChange)\n      document.removeEventListener('keydown', handleKeyDown)\n    }\n  }, [])\n\n  const togglePlay = () => {\n    const video = videoRef.current\n    if (!video) return\n\n    if (isPlaying) {\n      video.pause()\n    } else {\n      video.play()\n    }\n    setIsPlaying(!isPlaying)\n  }\n\n  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const video = videoRef.current\n    if (!video) return\n\n    const newTime = parseFloat(e.target.value)\n    video.currentTime = newTime\n    setCurrentTime(newTime)\n  }\n\n  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const video = videoRef.current\n    if (!video) return\n\n    const newVolume = parseFloat(e.target.value)\n    video.volume = newVolume\n    setVolume(newVolume)\n    setIsMuted(newVolume === 0)\n  }\n\n  const toggleMute = () => {\n    const video = videoRef.current\n    if (!video) return\n\n    if (isMuted) {\n      video.volume = volume\n      setIsMuted(false)\n    } else {\n      video.volume = 0\n      setIsMuted(true)\n    }\n  }\n\n  const toggleFullscreen = async () => {\n    const videoContainer = videoRef.current?.parentElement\n    if (!videoContainer) return\n\n    try {\n      if (!document.fullscreenElement) {\n        // 进入全屏\n        await videoContainer.requestFullscreen()\n        setIsFullscreen(true)\n      } else {\n        // 退出全屏\n        await document.exitFullscreen()\n        setIsFullscreen(false)\n      }\n    } catch (error) {\n      console.error('全屏切换失败:', error)\n    }\n  }\n\n  const formatTime = (time: number) => {\n    const minutes = Math.floor(time / 60)\n    const seconds = Math.floor(time % 60)\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`\n  }\n\n  // 初始化字幕轨道\n  const initializeSubtitleTracks = () => {\n    const tracks: SubtitleTrack[] = [\n      {\n        id: 'none',\n        label: '无字幕',\n        language: 'none',\n        src: ''\n      },\n      {\n        id: 'zh',\n        label: '中文字幕',\n        language: 'zh',\n        src: '/subtitles/default_zh.vtt'\n      },\n      {\n        id: 'zh-ko',\n        label: '中韩字幕 (中文+韩文)',\n        language: 'zh-ko',\n        src: '/subtitles/default_zh-ko.vtt'\n      },\n      {\n        id: 'zh-en',\n        label: '中英字幕 (中文+英文)',\n        language: 'zh-en',\n        src: '/subtitles/default_zh-en.vtt'\n      },\n      {\n        id: 'korean',\n        label: '한국어 (韩文)',\n        language: 'ko',\n        src: ''\n      },\n      {\n        id: 'english',\n        label: 'English (英文)',\n        language: 'en',\n        src: ''\n      }\n    ]\n    setSubtitleTracks(tracks)\n    // 默认选择中文字幕\n    setSelectedSubtitle('zh')\n  }\n\n  // 生成字幕\n  const generateSubtitles = async (language: string) => {\n    setIsGeneratingSubtitles(true)\n    try {\n      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)\n\n      const response = await fetch('/api/subtitles/generate', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Basic ${credentials}`\n        },\n        body: JSON.stringify({\n          videoPath,\n          language\n        })\n      })\n\n      const result = await response.json()\n\n      if (result.success) {\n        // 更新字幕轨道\n        setSubtitleTracks(prev =>\n          prev.map(track =>\n            track.language === language\n              ? { ...track, src: result.subtitlePath }\n              : track\n          )\n        )\n\n        // 根据语言类型自动选择对应的字幕ID\n        let subtitleId = 'english' // 默认\n        switch (language) {\n          case 'zh':\n            subtitleId = 'zh'\n            break\n          case 'ko':\n            subtitleId = 'korean'\n            break\n          case 'en':\n            subtitleId = 'english'\n            break\n          case 'zh-ko':\n            subtitleId = 'zh-ko'\n            break\n          case 'zh-en':\n            subtitleId = 'zh-en'\n            break\n        }\n\n        setSelectedSubtitle(subtitleId)\n\n        // 动态添加字幕轨道到视频元素\n        setTimeout(() => {\n          const video = videoRef.current\n          if (video) {\n            // 创建新的track元素\n            const trackElement = document.createElement('track')\n            trackElement.kind = 'subtitles'\n            trackElement.src = result.subtitlePath\n            trackElement.srclang = language.split('-')[0] // 取主要语言代码\n            trackElement.label = subtitleTracks.find(t => t.language === language)?.label || language\n            trackElement.default = true\n\n            // 添加到video元素\n            video.appendChild(trackElement)\n\n            // 启用字幕显示\n            trackElement.addEventListener('load', () => {\n              trackElement.track.mode = 'showing'\n            })\n          }\n        }, 100)\n\n      } else {\n        throw new Error(result.error || '字幕生成失败')\n      }\n\n    } catch (error) {\n      console.error('字幕生成失败:', error)\n      alert('字幕生成失败，请稍后重试')\n    } finally {\n      setIsGeneratingSubtitles(false)\n    }\n  }\n\n  // 切换字幕\n  const handleSubtitleChange = (subtitleId: string, event?: React.MouseEvent) => {\n    // 阻止事件冒泡，防止触发播放/暂停\n    if (event) {\n      event.preventDefault()\n      event.stopPropagation()\n    }\n\n    const video = videoRef.current\n    if (!video) return\n\n    // 禁用所有现有字幕轨道\n    const tracks = video.textTracks\n    for (let i = 0; i < tracks.length; i++) {\n      tracks[i].mode = 'disabled'\n    }\n\n    if (subtitleId === 'off' || subtitleId === 'none') {\n      setSelectedSubtitle('off')\n      setShowSubtitleMenu(false)\n      return\n    }\n\n    const selectedTrack = subtitleTracks.find(track => track.id === subtitleId)\n    if (selectedTrack) {\n      setSelectedSubtitle(subtitleId)\n\n      if (!selectedTrack.src) {\n        // 如果字幕文件不存在，生成字幕\n        generateSubtitles(selectedTrack.language)\n      } else {\n        // 启用对应的字幕轨道\n        setTimeout(() => {\n          const tracks = video.textTracks\n          for (let i = 0; i < tracks.length; i++) {\n            const track = tracks[i]\n            // 通过标签匹配字幕轨道\n            if (track.label === selectedTrack.label ||\n                (selectedTrack.id === 'zh' && track.label.includes('中文')) ||\n                (selectedTrack.id === 'zh-ko' && track.label.includes('中韩')) ||\n                (selectedTrack.id === 'zh-en' && track.label.includes('中英'))) {\n              track.mode = 'showing'\n              console.log('Enabled subtitle track:', track.label)\n              break\n            }\n          }\n        }, 100)\n      }\n    }\n    setShowSubtitleMenu(false)\n  }\n\n  const showControlsTemporarily = () => {\n    setShowControls(true)\n    if (controlsTimeoutRef.current) {\n      clearTimeout(controlsTimeoutRef.current)\n    }\n    controlsTimeoutRef.current = setTimeout(() => {\n      setShowControls(false)\n    }, 3000)\n  }\n\n  // 视频加载完成处理\n  const handleVideoLoaded = () => {\n    const video = videoRef.current\n    if (video) {\n      setDuration(video.duration)\n      // 启用默认字幕\n      if (selectedSubtitle !== 'off' && selectedSubtitle !== 'none') {\n        setTimeout(() => {\n          const tracks = video.textTracks\n          for (let i = 0; i < tracks.length; i++) {\n            const track = tracks[i]\n            if (track.label.includes('中文') && selectedSubtitle === 'zh') {\n              track.mode = 'showing'\n              break\n            }\n          }\n        }, 100)\n      }\n    }\n  }\n\n  // 时间更新处理\n  const handleTimeUpdate = () => {\n    const video = videoRef.current\n    if (video) {\n      setCurrentTime(video.currentTime)\n    }\n  }\n\n  // 时长变化处理\n  const handleDurationChange = () => {\n    const video = videoRef.current\n    if (video) {\n      setDuration(video.duration)\n    }\n  }\n\n  // 视频错误处理\n  const handleVideoError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {\n    console.error('Video error:', e)\n    const video = e.currentTarget\n    if (video.error) {\n      console.error('Video error details:', {\n        code: video.error.code,\n        message: video.error.message\n      })\n    }\n  }\n\n  // 构建认证的视频URL\n  const getAuthenticatedVideoUrl = () => {\n    try {\n      const username = localStorage.getItem('admin_username') || 'admin'\n      const password = localStorage.getItem('admin_password') || 'admin123'\n      const credentials = btoa(`${username}:${password}`)\n      const fileName = videoPath.split('/').pop()\n      return `/api/video/${fileName}?auth=${encodeURIComponent(credentials)}&t=${Date.now()}`\n    } catch (error) {\n      console.error('Error generating video URL:', error)\n      return ''\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50 flex items-center justify-center\">\n      <div className={`relative w-full h-full ${isFullscreen ? '' : 'max-w-6xl max-h-full'}`}>\n        {/* 关闭按钮 */}\n        <button\n          onClick={onClose}\n          className=\"absolute top-4 right-4 z-10 w-10 h-10 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center transition-colors\"\n        >\n          <X className=\"text-white\" size={24} />\n        </button>\n\n        {/* 视频标题 */}\n        <div className=\"absolute top-4 left-4 z-10 bg-black/50 backdrop-blur-sm rounded px-4 py-2\">\n          <h2 className=\"text-white text-lg font-semibold\">{title}</h2>\n        </div>\n\n        {/* 视频容器 */}\n        <div \n          className=\"relative w-full h-full cursor-pointer\"\n          onMouseMove={showControlsTemporarily}\n          onClick={togglePlay}\n        >\n          {isLoading && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-black\">\n              <div className=\"text-white text-center\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"></div>\n                <p>加载中...</p>\n              </div>\n            </div>\n          )}\n\n          {error && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-black\">\n              <div className=\"text-white text-center\">\n                <p className=\"text-red-400 mb-4\">{error}</p>\n                <button\n                  onClick={onClose}\n                  className=\"px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded transition-colors\"\n                >\n                  关闭\n                </button>\n              </div>\n            </div>\n          )}\n\n          <video\n            ref={videoRef}\n            className=\"w-full h-full object-contain\"\n            src={getAuthenticatedVideoUrl()}\n            onPlay={() => setIsPlaying(true)}\n            onPause={() => setIsPlaying(false)}\n            onLoadedMetadata={handleVideoLoaded}\n            onTimeUpdate={handleTimeUpdate}\n            onDurationChange={handleDurationChange}\n            onError={handleVideoError}\n            crossOrigin=\"anonymous\"\n            preload=\"metadata\"\n            playsInline\n            controls={false}\n          >\n            {/* 字幕轨道 */}\n            {subtitleTracks.map((track) => (\n              track.src && track.id !== 'none' && (\n                <track\n                  key={track.id}\n                  kind=\"subtitles\"\n                  src={track.src}\n                  srcLang={track.language.split('-')[0]}\n                  label={track.label}\n                  default={selectedSubtitle === track.id}\n                />\n              )\n            ))}\n          </video>\n\n          {/* 播放控制栏 */}\n          {showControls && !isLoading && !error && (\n            <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4\">\n              {/* 进度条 */}\n              <div className=\"mb-4\">\n                <input\n                  type=\"range\"\n                  min=\"0\"\n                  max={duration || 0}\n                  value={currentTime}\n                  onChange={handleSeek}\n                  className=\"w-full h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer slider\"\n                />\n              </div>\n\n              {/* 控制按钮 */}\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-4\">\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      togglePlay()\n                    }}\n                    className=\"text-white hover:text-gray-300 transition-colors\"\n                  >\n                    {isPlaying ? <Pause size={24} /> : <Play size={24} />}\n                  </button>\n\n                  <div className=\"flex items-center space-x-2\">\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation()\n                        toggleMute()\n                      }}\n                      className=\"text-white hover:text-gray-300 transition-colors\"\n                    >\n                      {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}\n                    </button>\n                    <input\n                      type=\"range\"\n                      min=\"0\"\n                      max=\"1\"\n                      step=\"0.1\"\n                      value={isMuted ? 0 : volume}\n                      onChange={handleVolumeChange}\n                      onClick={(e) => e.stopPropagation()}\n                      className=\"w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer\"\n                    />\n                  </div>\n\n                  <span className=\"text-white text-sm\">\n                    {formatTime(currentTime)} / {formatTime(duration)}\n                  </span>\n                </div>\n\n                <div className=\"flex items-center space-x-2\">\n                  {/* 字幕按钮 */}\n                  <div className=\"relative\">\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation()\n                        setShowSubtitleMenu(!showSubtitleMenu)\n                      }}\n                      className={`text-white hover:text-gray-300 transition-colors ${\n                        selectedSubtitle !== 'off' ? 'text-red-400' : ''\n                      }`}\n                      title=\"字幕设置\"\n                    >\n                      <Subtitles size={20} />\n                    </button>\n\n                    {/* 字幕菜单 */}\n                    {showSubtitleMenu && (\n                      <div\n                        className=\"absolute bottom-full right-0 mb-2 bg-black/90 backdrop-blur-sm rounded-lg p-2 min-w-[150px]\"\n                        onClick={(e) => e.stopPropagation()}\n                      >\n                        <div className=\"text-white text-sm font-semibold mb-2 px-2\">字幕选择</div>\n\n                        {/* 字幕轨道选项 */}\n                        {subtitleTracks.map((track) => (\n                          <button\n                            key={track.id}\n                            onClick={(e) => handleSubtitleChange(track.id, e)}\n                            className={`w-full text-left px-2 py-1 text-sm rounded transition-colors flex items-center justify-between ${\n                              selectedSubtitle === track.id\n                                ? 'bg-red-600 text-white'\n                                : 'text-gray-300 hover:bg-gray-700'\n                            }`}\n                          >\n                            <span>{track.label}</span>\n                            {!track.src && (\n                              <span className=\"text-xs text-gray-400\">\n                                {isGeneratingSubtitles ? '生成中...' : '生成'}\n                              </span>\n                            )}\n                          </button>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      toggleFullscreen()\n                    }}\n                    className=\"text-white hover:text-gray-300 transition-colors\"\n                  >\n                    <Maximize size={20} />\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAmBe,SAAS,YAAY,KAA+C;QAA/C,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAoB,GAA/C;;IAClC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,QAAQ,SAAS,OAAO;YAC9B,IAAI,CAAC,OAAO;YAEZ,MAAM;8DAAuB;oBAC3B,YAAY,MAAM,QAAQ;oBAC1B,aAAa;oBACb,UAAU;oBACV;gBACF;;YAEA,MAAM;0DAAmB;oBACvB,eAAe,MAAM,WAAW;gBAClC;;YAEA,MAAM;qDAAc;oBAClB,SAAS;oBACT,aAAa;gBACf;;YAEA,MAAM;yDAAkB;oBACtB,aAAa;oBACb,SAAS;gBACX;;YAEA,WAAW;YACX,MAAM;gEAAyB;oBAC7B,gBAAgB,CAAC,CAAC,SAAS,iBAAiB;gBAC9C;;YAEA,SAAS;YACT,MAAM;uDAAgB,CAAC;oBACrB,IAAI,EAAE,GAAG,KAAK,YAAY,SAAS,iBAAiB,EAAE;wBACpD,SAAS,cAAc;oBACzB,OAAO,IAAI,EAAE,GAAG,KAAK,KAAK;wBACxB,EAAE,cAAc;wBAChB;oBACF;gBACF;;YAEA,MAAM,gBAAgB,CAAC,kBAAkB;YACzC,MAAM,gBAAgB,CAAC,cAAc;YACrC,MAAM,gBAAgB,CAAC,SAAS;YAChC,MAAM,gBAAgB,CAAC,aAAa;YACpC,SAAS,gBAAgB,CAAC,oBAAoB;YAC9C,SAAS,gBAAgB,CAAC,WAAW;YAErC;yCAAO;oBACL,MAAM,mBAAmB,CAAC,kBAAkB;oBAC5C,MAAM,mBAAmB,CAAC,cAAc;oBACxC,MAAM,mBAAmB,CAAC,SAAS;oBACnC,MAAM,mBAAmB,CAAC,aAAa;oBACvC,SAAS,mBAAmB,CAAC,oBAAoB;oBACjD,SAAS,mBAAmB,CAAC,WAAW;gBAC1C;;QACF;gCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,IAAI,WAAW;YACb,MAAM,KAAK;QACb,OAAO;YACL,MAAM,IAAI;QACZ;QACA,aAAa,CAAC;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,UAAU,WAAW,EAAE,MAAM,CAAC,KAAK;QACzC,MAAM,WAAW,GAAG;QACpB,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,YAAY,WAAW,EAAE,MAAM,CAAC,KAAK;QAC3C,MAAM,MAAM,GAAG;QACf,UAAU;QACV,WAAW,cAAc;IAC3B;IAEA,MAAM,aAAa;QACjB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,IAAI,SAAS;YACX,MAAM,MAAM,GAAG;YACf,WAAW;QACb,OAAO;YACL,MAAM,MAAM,GAAG;YACf,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;YACA;QAAvB,MAAM,kBAAiB,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,aAAa;QACtD,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,IAAI,CAAC,SAAS,iBAAiB,EAAE;gBAC/B,OAAO;gBACP,MAAM,eAAe,iBAAiB;gBACtC,gBAAgB;YAClB,OAAO;gBACL,OAAO;gBACP,MAAM,SAAS,cAAc;gBAC7B,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,OAAO,AAAC,GAAa,OAAX,SAAQ,KAAuC,OAApC,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACtD;IAEA,UAAU;IACV,MAAM,2BAA2B;QAC/B,MAAM,SAA0B;YAC9B;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,KAAK;YACP;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,KAAK;YACP;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,KAAK;YACP;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,KAAK;YACP;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,KAAK;YACP;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,KAAK;YACP;SACD;QACD,kBAAkB;QAClB,WAAW;QACX,oBAAoB;IACtB;IAEA,OAAO;IACP,MAAM,oBAAoB,OAAO;QAC/B,yBAAyB;QACzB,IAAI;YACF,MAAM,cAAc,KAAK,AAAC,GAA4C,OAA1C,aAAa,OAAO,CAAC,mBAAkB,KAA0C,OAAvC,aAAa,OAAO,CAAC;YAE3F,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,SAAoB,OAAZ;gBAC5B;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS;gBACT,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,QAAQ,KAAK,WACf;4BAAE,GAAG,KAAK;4BAAE,KAAK,OAAO,YAAY;wBAAC,IACrC;gBAIR,oBAAoB;gBACpB,IAAI,aAAa,UAAU,KAAK;;gBAChC,OAAQ;oBACN,KAAK;wBACH,aAAa;wBACb;oBACF,KAAK;wBACH,aAAa;wBACb;oBACF,KAAK;wBACH,aAAa;wBACb;oBACF,KAAK;wBACH,aAAa;wBACb;oBACF,KAAK;wBACH,aAAa;wBACb;gBACJ;gBAEA,oBAAoB;gBAEpB,gBAAgB;gBAChB,WAAW;oBACT,MAAM,QAAQ,SAAS,OAAO;oBAC9B,IAAI,OAAO;4BAMY;wBALrB,cAAc;wBACd,MAAM,eAAe,SAAS,aAAa,CAAC;wBAC5C,aAAa,IAAI,GAAG;wBACpB,aAAa,GAAG,GAAG,OAAO,YAAY;wBACtC,aAAa,OAAO,GAAG,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,EAAC,UAAU;wBACxD,aAAa,KAAK,GAAG,EAAA,uBAAA,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,uBAAxC,2CAAA,qBAAmD,KAAK,KAAI;wBACjF,aAAa,OAAO,GAAG;wBAEvB,aAAa;wBACb,MAAM,WAAW,CAAC;wBAElB,SAAS;wBACT,aAAa,gBAAgB,CAAC,QAAQ;4BACpC,aAAa,KAAK,CAAC,IAAI,GAAG;wBAC5B;oBACF;gBACF,GAAG;YAEL,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,yBAAyB;QAC3B;IACF;IAEA,OAAO;IACP,MAAM,uBAAuB,CAAC,YAAoB;QAChD,mBAAmB;QACnB,IAAI,OAAO;YACT,MAAM,cAAc;YACpB,MAAM,eAAe;QACvB;QAEA,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,aAAa;QACb,MAAM,SAAS,MAAM,UAAU;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;QACnB;QAEA,IAAI,eAAe,SAAS,eAAe,QAAQ;YACjD,oBAAoB;YACpB,oBAAoB;YACpB;QACF;QAEA,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAChE,IAAI,eAAe;YACjB,oBAAoB;YAEpB,IAAI,CAAC,cAAc,GAAG,EAAE;gBACtB,iBAAiB;gBACjB,kBAAkB,cAAc,QAAQ;YAC1C,OAAO;gBACL,YAAY;gBACZ,WAAW;oBACT,MAAM,SAAS,MAAM,UAAU;oBAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;wBACtC,MAAM,QAAQ,MAAM,CAAC,EAAE;wBACvB,aAAa;wBACb,IAAI,MAAM,KAAK,KAAK,cAAc,KAAK,IAClC,cAAc,EAAE,KAAK,QAAQ,MAAM,KAAK,CAAC,QAAQ,CAAC,SAClD,cAAc,EAAE,KAAK,WAAW,MAAM,KAAK,CAAC,QAAQ,CAAC,SACrD,cAAc,EAAE,KAAK,WAAW,MAAM,KAAK,CAAC,QAAQ,CAAC,OAAQ;4BAChE,MAAM,IAAI,GAAG;4BACb,QAAQ,GAAG,CAAC,2BAA2B,MAAM,KAAK;4BAClD;wBACF;oBACF;gBACF,GAAG;YACL;QACF;QACA,oBAAoB;IACtB;IAEA,MAAM,0BAA0B;QAC9B,gBAAgB;QAChB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,aAAa,mBAAmB,OAAO;QACzC;QACA,mBAAmB,OAAO,GAAG,WAAW;YACtC,gBAAgB;QAClB,GAAG;IACL;IAEA,WAAW;IACX,MAAM,oBAAoB;QACxB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,OAAO;YACT,YAAY,MAAM,QAAQ;YAC1B,SAAS;YACT,IAAI,qBAAqB,SAAS,qBAAqB,QAAQ;gBAC7D,WAAW;oBACT,MAAM,SAAS,MAAM,UAAU;oBAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;wBACtC,MAAM,QAAQ,MAAM,CAAC,EAAE;wBACvB,IAAI,MAAM,KAAK,CAAC,QAAQ,CAAC,SAAS,qBAAqB,MAAM;4BAC3D,MAAM,IAAI,GAAG;4BACb;wBACF;oBACF;gBACF,GAAG;YACL;QACF;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB;QACvB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,OAAO;YACT,eAAe,MAAM,WAAW;QAClC;IACF;IAEA,SAAS;IACT,MAAM,uBAAuB;QAC3B,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,OAAO;YACT,YAAY,MAAM,QAAQ;QAC5B;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB,CAAC;QACxB,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,MAAM,QAAQ,EAAE,aAAa;QAC7B,IAAI,MAAM,KAAK,EAAE;YACf,QAAQ,KAAK,CAAC,wBAAwB;gBACpC,MAAM,MAAM,KAAK,CAAC,IAAI;gBACtB,SAAS,MAAM,KAAK,CAAC,OAAO;YAC9B;QACF;IACF;IAEA,aAAa;IACb,MAAM,2BAA2B;QAC/B,IAAI;YACF,MAAM,WAAW,aAAa,OAAO,CAAC,qBAAqB;YAC3D,MAAM,WAAW,aAAa,OAAO,CAAC,qBAAqB;YAC3D,MAAM,cAAc,KAAK,AAAC,GAAc,OAAZ,UAAS,KAAY,OAAT;YACxC,MAAM,WAAW,UAAU,KAAK,CAAC,KAAK,GAAG;YACzC,OAAO,AAAC,cAA8B,OAAjB,UAAS,UAA6C,OAArC,mBAAmB,cAAa,OAAgB,OAAX,KAAK,GAAG;QACrF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAW,AAAC,0BAAoE,OAA3C,eAAe,KAAK;;8BAE5D,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;wBAAa,MAAM;;;;;;;;;;;8BAIlC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;;;;;;8BAIpD,6LAAC;oBACC,WAAU;oBACV,aAAa;oBACb,SAAS;;wBAER,2BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;kDAAE;;;;;;;;;;;;;;;;;wBAKR,uBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOP,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,KAAK;4BACL,QAAQ,IAAM,aAAa;4BAC3B,SAAS,IAAM,aAAa;4BAC5B,kBAAkB;4BAClB,cAAc;4BACd,kBAAkB;4BAClB,SAAS;4BACT,aAAY;4BACZ,SAAQ;4BACR,WAAW;4BACX,UAAU;sCAGT,eAAe,GAAG,CAAC,CAAC,QACnB,MAAM,GAAG,IAAI,MAAM,EAAE,KAAK,wBACxB,6LAAC;oCAEC,MAAK;oCACL,KAAK,MAAM,GAAG;oCACd,SAAS,MAAM,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oCACrC,OAAO,MAAM,KAAK;oCAClB,SAAS,qBAAqB,MAAM,EAAE;mCALjC,MAAM,EAAE;;;;;;;;;;wBAYpB,gBAAgB,CAAC,aAAa,CAAC,uBAC9B,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,KAAK,YAAY;wCACjB,OAAO;wCACP,UAAU;wCACV,WAAU;;;;;;;;;;;8CAKd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB;oDACF;oDACA,WAAU;8DAET,0BAAY,6LAAC,uMAAA,CAAA,QAAK;wDAAC,MAAM;;;;;6EAAS,6LAAC,qMAAA,CAAA,OAAI;wDAAC,MAAM;;;;;;;;;;;8DAGjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB;4DACF;4DACA,WAAU;sEAET,wBAAU,6LAAC,+MAAA,CAAA,UAAO;gEAAC,MAAM;;;;;qFAAS,6LAAC,+MAAA,CAAA,UAAO;gEAAC,MAAM;;;;;;;;;;;sEAEpD,6LAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,MAAK;4DACL,OAAO,UAAU,IAAI;4DACrB,UAAU;4DACV,SAAS,CAAC,IAAM,EAAE,eAAe;4DACjC,WAAU;;;;;;;;;;;;8DAId,6LAAC;oDAAK,WAAU;;wDACb,WAAW;wDAAa;wDAAI,WAAW;;;;;;;;;;;;;sDAI5C,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,oBAAoB,CAAC;4DACvB;4DACA,WAAW,AAAC,oDAEX,OADC,qBAAqB,QAAQ,iBAAiB;4DAEhD,OAAM;sEAEN,cAAA,6LAAC,8MAAA,CAAA,YAAS;gEAAC,MAAM;;;;;;;;;;;wDAIlB,kCACC,6LAAC;4DACC,WAAU;4DACV,SAAS,CAAC,IAAM,EAAE,eAAe;;8EAEjC,6LAAC;oEAAI,WAAU;8EAA6C;;;;;;gEAG3D,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC;wEAEC,SAAS,CAAC,IAAM,qBAAqB,MAAM,EAAE,EAAE;wEAC/C,WAAW,AAAC,kGAIX,OAHC,qBAAqB,MAAM,EAAE,GACzB,0BACA;;0FAGN,6LAAC;0FAAM,MAAM,KAAK;;;;;;4EACjB,CAAC,MAAM,GAAG,kBACT,6LAAC;gFAAK,WAAU;0FACb,wBAAwB,WAAW;;;;;;;uEAXnC,MAAM,EAAE;;;;;;;;;;;;;;;;;8DAoBvB,6LAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB;oDACF;oDACA,WAAU;8DAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpC;GAllBwB;KAAA", "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/movie/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Film, Star, ExternalLink, Calendar, Play } from 'lucide-react'\nimport Image from 'next/image'\nimport VideoPlayer from '@/components/VideoPlayer'\n\ninterface Movie {\n  id: string\n  title: string\n  description: string\n  poster_url?: string\n  movie_link?: string\n  video_file_path?: string\n  category: string\n  rating?: number\n  release_year?: number\n  created_at: string\n}\n\nexport default function MoviePage() {\n  const [movies, setMovies] = useState<Movie[]>([])\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n  const [isLoading, setIsLoading] = useState(true)\n  const [selectedMovie, setSelectedMovie] = useState<Movie | null>(null)\n  const [isAuthenticated, setIsAuthenticated] = useState(false)\n\n  const categories = [\n    { id: 'all', label: '全部' },\n    { id: 'korea', label: '韩国' },\n    { id: 'usa', label: '美国' },\n    { id: 'taiwan', label: '台湾' },\n    { id: 'japan', label: '日本' },\n  ]\n\n  useEffect(() => {\n    fetchMovies()\n    checkAuthentication()\n  }, [])\n\n  const checkAuthentication = () => {\n    const username = localStorage.getItem('admin_username')\n    const password = localStorage.getItem('admin_password')\n    setIsAuthenticated(!!(username && password))\n  }\n\n  const fetchMovies = async () => {\n    try {\n      const response = await fetch('/api/movies')\n      if (response.ok) {\n        const data = await response.json()\n        setMovies(data.data)\n      }\n    } catch (error) {\n      console.error('Failed to fetch movies:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handlePlayMovie = (movie: Movie) => {\n    if (!isAuthenticated) {\n      alert('请先登录管理后台才能观看影片')\n      return\n    }\n\n    if (!movie.video_file_path) {\n      alert('该影片暂无视频资源')\n      return\n    }\n\n    setSelectedMovie(movie)\n  }\n\n  const filteredMovies = selectedCategory === 'all'\n    ? movies\n    : movies.filter(movie => movie.category === selectedCategory)\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen py-12 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-red-400 mx-auto mb-4\"></div>\n          <p className=\"text-gray-400\">Loading movies...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen py-12\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"flex justify-center mb-6\">\n            <Film className=\"text-red-400\" size={48} />\n          </div>\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-6 tracking-wider\">MOVIES</h1>\n          <p className=\"text-xl text-gray-400 max-w-2xl mx-auto\">\n            Discover the films that inspire me - from classic gangster movies to emotional dramas and historical epics\n          </p>\n        </div>\n\n        {/* Category Filter */}\n        <div className=\"mb-12\">\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {categories.map((category) => (\n              <button\n                key={category.id}\n                onClick={() => setSelectedCategory(category.id)}\n                className={`px-6 py-3 rounded-full font-semibold transition-all duration-200 ${\n                  selectedCategory === category.id\n                    ? 'bg-red-600 text-white shadow-lg'\n                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white'\n                }`}\n              >\n                {category.label}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Movies Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\n          {filteredMovies.map((movie) => (\n            <div\n              key={movie.id}\n              className=\"bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-all duration-300 hover:scale-105 hover:shadow-xl\"\n            >\n              {/* Movie Poster */}\n              <div className=\"relative aspect-[2/3] bg-gray-600\">\n                {movie.poster_url ? (\n                  <Image\n                    src={movie.poster_url}\n                    alt={movie.title}\n                    fill\n                    className=\"object-cover\"\n                    onError={(e) => {\n                      // 图片加载失败时隐藏图片，显示默认图标\n                      const target = e.target as HTMLImageElement;\n                      target.style.display = 'none';\n                      const parent = target.parentElement;\n                      if (parent) {\n                        parent.innerHTML = `\n                          <div class=\"flex items-center justify-center h-full\">\n                            <svg class=\"text-gray-400\" width=\"64\" height=\"64\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4zM6 6v12h12V6H6zm3 3h6v6H9V9z\"/>\n                            </svg>\n                          </div>\n                        `;\n                      }\n                    }}\n                  />\n                ) : (\n                  <div className=\"flex items-center justify-center h-full\">\n                    <Film className=\"text-gray-400\" size={64} />\n                  </div>\n                )}\n                \n                {/* Rating Badge */}\n                {movie.rating && (\n                  <div className=\"absolute top-4 right-4 bg-black/70 backdrop-blur-sm rounded-full px-3 py-1 flex items-center\">\n                    <Star className=\"text-yellow-400 mr-1\" size={16} fill=\"currentColor\" />\n                    <span className=\"text-white text-sm font-semibold\">{movie.rating}</span>\n                  </div>\n                )}\n\n                {/* External Link Button */}\n                {movie.movie_link && (\n                  <div className=\"absolute bottom-4 right-4\">\n                    <a\n                      href={movie.movie_link}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"w-10 h-10 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center transition-colors duration-200\"\n                      title=\"观看电影\"\n                    >\n                      <ExternalLink className=\"text-white\" size={20} />\n                    </a>\n                  </div>\n                )}\n              </div>\n\n              {/* Movie Info */}\n              <div className=\"p-6\">\n                <h3 className=\"text-xl font-semibold mb-2 line-clamp-2\">{movie.title}</h3>\n                \n                {/* Category and Year */}\n                <div className=\"flex items-center justify-between mb-3\">\n                  <span className=\"px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded-full\">\n                    {categories.find(cat => cat.id === movie.category)?.label || movie.category}\n                  </span>\n                  {movie.release_year && (\n                    <div className=\"flex items-center text-gray-400 text-sm\">\n                      <Calendar className=\"mr-1\" size={14} />\n                      <span>{movie.release_year}</span>\n                    </div>\n                  )}\n                </div>\n\n                {/* Description */}\n                <p className=\"text-gray-400 text-sm line-clamp-3 mb-4\">\n                  {movie.description}\n                </p>\n\n                {/* Action Buttons */}\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-xs text-gray-500\">\n                      添加于 {new Date(movie.created_at).toLocaleDateString('zh-CN')}\n                    </span>\n                  </div>\n\n                  <div className=\"flex space-x-2\">\n                    {/* 本地视频播放按钮 */}\n                    {movie.video_file_path ? (\n                      <button\n                        onClick={() => handlePlayMovie(movie)}\n                        className={`flex-1 flex items-center justify-center px-4 py-2 rounded transition-colors duration-200 ${\n                          isAuthenticated\n                            ? 'bg-red-600 hover:bg-red-700 text-white'\n                            : 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                        }`}\n                        disabled={!isAuthenticated}\n                      >\n                        <Play className=\"mr-2\" size={16} />\n                        {isAuthenticated ? '观看影片' : '需要登录'}\n                      </button>\n                    ) : (\n                      <div className=\"flex-1 flex items-center justify-center px-4 py-2 bg-gray-700 text-gray-500 rounded\">\n                        <Play className=\"mr-2\" size={16} />\n                        暂无资源\n                      </div>\n                    )}\n\n                    {/* 外部链接按钮 */}\n                    {movie.movie_link && (\n                      <a\n                        href={movie.movie_link}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors duration-200 flex items-center\"\n                        title=\"外部观看链接\"\n                      >\n                        <ExternalLink size={16} />\n                      </a>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Empty State */}\n        {filteredMovies.length === 0 && (\n          <div className=\"text-center py-20\">\n            <Film className=\"mx-auto text-gray-600 mb-4\" size={64} />\n            <h3 className=\"text-2xl font-semibold text-gray-400 mb-2\">\n              {selectedCategory === 'all' ? 'No Movies Available' : 'No Movies in This Category'}\n            </h3>\n            <p className=\"text-gray-500\">\n              {selectedCategory === 'all' \n                ? 'Stay tuned for movie recommendations' \n                : 'Try selecting a different category'}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* 视频播放器 */}\n      {selectedMovie && selectedMovie.video_file_path && (\n        <VideoPlayer\n          videoPath={selectedMovie.video_file_path}\n          title={selectedMovie.title}\n          onClose={() => setSelectedMovie(null)}\n        />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAoBe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,OAAO;QAAK;QACzB;YAAE,IAAI;YAAS,OAAO;QAAK;QAC3B;YAAE,IAAI;YAAO,OAAO;QAAK;QACzB;YAAE,IAAI;YAAU,OAAO;QAAK;QAC5B;YAAE,IAAI;YAAS,OAAO;QAAK;KAC5B;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;YACA;QACF;8BAAG,EAAE;IAEL,MAAM,sBAAsB;QAC1B,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,mBAAmB,CAAC,CAAC,CAAC,YAAY,QAAQ;IAC5C;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,UAAU,KAAK,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,iBAAiB;YACpB,MAAM;YACN;QACF;QAEA,IAAI,CAAC,MAAM,eAAe,EAAE;YAC1B,MAAM;YACN;QACF;QAEA,iBAAiB;IACnB;IAEA,MAAM,iBAAiB,qBAAqB,QACxC,SACA,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;IAE9C,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAe,MAAM;;;;;;;;;;;0CAEvC,6LAAC;gCAAG,WAAU;0CAAqD;;;;;;0CACnE,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;oCAEC,SAAS,IAAM,oBAAoB,SAAS,EAAE;oCAC9C,WAAW,AAAC,oEAIX,OAHC,qBAAqB,SAAS,EAAE,GAC5B,oCACA;8CAGL,SAAS,KAAK;mCARV,SAAS,EAAE;;;;;;;;;;;;;;;kCAexB,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC;gCAkEV;iDAjET,6LAAC;gCAEC,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;;4CACZ,MAAM,UAAU,iBACf,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,MAAM,UAAU;gDACrB,KAAK,MAAM,KAAK;gDAChB,IAAI;gDACJ,WAAU;gDACV,SAAS,CAAC;oDACR,qBAAqB;oDACrB,MAAM,SAAS,EAAE,MAAM;oDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;oDACvB,MAAM,SAAS,OAAO,aAAa;oDACnC,IAAI,QAAQ;wDACV,OAAO,SAAS,GAAI;oDAOtB;gDACF;;;;;qEAGF,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;oDAAgB,MAAM;;;;;;;;;;;4CAKzC,MAAM,MAAM,kBACX,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;wDAAuB,MAAM;wDAAI,MAAK;;;;;;kEACtD,6LAAC;wDAAK,WAAU;kEAAoC,MAAM,MAAM;;;;;;;;;;;;4CAKnE,MAAM,UAAU,kBACf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,MAAM,MAAM,UAAU;oDACtB,QAAO;oDACP,KAAI;oDACJ,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;wDAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;kDAOnD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C,MAAM,KAAK;;;;;;0DAGpE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,EAAA,mBAAA,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,MAAM,QAAQ,eAAhD,uCAAA,iBAAmD,KAAK,KAAI,MAAM,QAAQ;;;;;;oDAE5E,MAAM,YAAY,kBACjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;gEAAO,MAAM;;;;;;0EACjC,6LAAC;0EAAM,MAAM,YAAY;;;;;;;;;;;;;;;;;;0DAM/B,6LAAC;gDAAE,WAAU;0DACV,MAAM,WAAW;;;;;;0DAIpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;;gEAAwB;gEACjC,IAAI,KAAK,MAAM,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;kEAIvD,6LAAC;wDAAI,WAAU;;4DAEZ,MAAM,eAAe,iBACpB,6LAAC;gEACC,SAAS,IAAM,gBAAgB;gEAC/B,WAAW,AAAC,4FAIX,OAHC,kBACI,2CACA;gEAEN,UAAU,CAAC;;kFAEX,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;wEAAO,MAAM;;;;;;oEAC5B,kBAAkB,SAAS;;;;;;qFAG9B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;wEAAO,MAAM;;;;;;oEAAM;;;;;;;4DAMtC,MAAM,UAAU,kBACf,6LAAC;gEACC,MAAM,MAAM,UAAU;gEACtB,QAAO;gEACP,KAAI;gEACJ,WAAU;gEACV,OAAM;0EAEN,cAAA,6LAAC,yNAAA,CAAA,eAAY;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAtHzB,MAAM,EAAE;;;;;;;;;;;oBAiIlB,eAAe,MAAM,KAAK,mBACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;gCAA6B,MAAM;;;;;;0CACnD,6LAAC;gCAAG,WAAU;0CACX,qBAAqB,QAAQ,wBAAwB;;;;;;0CAExD,6LAAC;gCAAE,WAAU;0CACV,qBAAqB,QAClB,yCACA;;;;;;;;;;;;;;;;;;YAOX,iBAAiB,cAAc,eAAe,kBAC7C,6LAAC,oIAAA,CAAA,UAAW;gBACV,WAAW,cAAc,eAAe;gBACxC,OAAO,cAAc,KAAK;gBAC1B,SAAS,IAAM,iBAAiB;;;;;;;;;;;;AAK1C;GApQwB;KAAA", "debugId": null}}]}